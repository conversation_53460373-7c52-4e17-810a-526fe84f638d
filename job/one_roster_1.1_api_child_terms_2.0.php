<?php

/*
* Copyright Abre.io Inc.
*/

// Enable garbage collection for better memory management
gc_enable();

require_once(dirname(__FILE__) . '/utils/functions.php');
require_once(dirname(__FILE__) . '/utils/logging.php');
require(dirname(__FILE__) . '/utils/OneRosterAPI.php');

use Google\Cloud\Storage\StorageClient;

// Streaming functions for large datasets
function _initGcsStream($type, $bucket, $currentDate, $siteID)
{
    try {
        $tempFile1 = tmpfile();
        $tempFile2 = tmpfile();
        
        if ($tempFile1 === false || $tempFile2 === false) {
            error_log("ERROR: Failed to create temporary files for {$type} GCS stream");
            return false;
        }
        
        $stream = [
            'type' => $type,
            'bucket' => $bucket,
            'currentDate' => $currentDate,
            'siteID' => $siteID,
            'tempFile1' => $tempFile1,
            'tempFile2' => $tempFile2,
            'count' => 0,
            'first' => true
        ];

        // Write opening bracket for JSON array
        fwrite($stream['tempFile1'], '[');
        fwrite($stream['tempFile2'], '[');
        
        // Stream initialized
        return $stream;
    } catch (Exception $e) {
        error_log("ERROR: Exception in _initGcsStream for {$type}: " . $e->getMessage());
        return false;
    }
}

function _appendToGcsStream(&$stream, $item)
{
    try {
        if (!$stream['first']) {
            fwrite($stream['tempFile1'], ',');
            fwrite($stream['tempFile2'], ',');
        }

        $json = json_encode($item);
        if ($json !== false) {
            fwrite($stream['tempFile1'], $json);
            fwrite($stream['tempFile2'], $json);
            $stream['count']++;
            $stream['first'] = false;
        } else {
            error_log("WARNING: Failed to JSON encode item for {$stream['type']} stream: " . json_last_error_msg());
        }
    } catch (Exception $e) {
        error_log("ERROR: Exception in _appendToGcsStream for {$stream['type']}: " . $e->getMessage());
    }
}

function _finalizeGcsStream(&$stream)
{
    try {
        if ($stream['count'] == 0) {
            fclose($stream['tempFile1']);
            fclose($stream['tempFile2']);
            error_log("No {$stream['type']} records to stream");
            return;
        }

        // Close JSON array
        fwrite($stream['tempFile1'], ']');
        fwrite($stream['tempFile2'], ']');

        // Upload to site-id folder
        rewind($stream['tempFile1']);
        $fileName = "Abre_OneRoster_1.1_API_ChildTerms_2.0_{$stream['type']}.json";
        try {
            $stream['bucket']->upload($stream['tempFile1'], [
                'name' => "{$stream['currentDate']}/site-id/{$stream['siteID']}/$fileName"
            ]);
            // Uploaded to site-id folder
        } catch (Exception $e) {
            error_log("ERROR: Failed to upload {$stream['type']} to site-id folder: " . $e->getMessage());
        }

        // Upload to filename folder
        rewind($stream['tempFile2']);
        $folderName = "Abre_OneRoster_1.1_API_ChildTerms_2.0_{$stream['type']}";
        try {
            $stream['bucket']->upload($stream['tempFile2'], [
                'name' => "{$stream['currentDate']}/filename/$folderName/$folderName-{$stream['siteID']}.json"
            ]);
            // Uploaded to filename folder
        } catch (Exception $e) {
            error_log("ERROR: Failed to upload {$stream['type']} to filename folder: " . $e->getMessage());
        }

        error_log("Successfully streamed {$stream['count']} {$stream['type']} records to GCS");
    } catch (Exception $e) {
        error_log("ERROR: Exception in _finalizeGcsStream for {$stream['type']}: " . $e->getMessage());
    } finally {
        // Always close file handles to prevent resource leaks
        if (isset($stream['tempFile1']) && is_resource($stream['tempFile1'])) {
            fclose($stream['tempFile1']);
        }
        if (isset($stream['tempFile2']) && is_resource($stream['tempFile2'])) {
            fclose($stream['tempFile2']);
        }
    }
}

function runJob($db, $siteID, $config)
{
    // Set resource limits to prevent runaway processes
    set_time_limit(43200);
    ini_set('memory_limit', '2G');
    
    // Add periodic memory monitoring
    $memoryCheckInterval = 1000;
    $memoryWarningThreshold = 800 * 1024 * 1024;
    
    $cronName = 'Abre One Roster 1.1 - API Child Terms 2.0';

    try {
        $uuid = Logger::logCronStart($db, $siteID, $cronName);

        define("MAX_IMPORT_LIMIT", 250);

        $jobStartTime = (new DateTime("now", new DateTimeZone('UTC')))->format("Y-m-d H:i:s");
        $schoolYearID = getCurrentSchoolYearID($db);
        $currentEndingSchoolYear = getCurrentEndSchoolYear($db);
        // School year initialized

        $error = null;
        $skip = null;
        $separator = "\r\n";

        $schoolsCache = [];
        $gradingPeriods = [];
        $usersCache = [];
        $teacherCache = [];
        $classTeachersIndex = []; // O(1) lookup: classSourcedId => [teacher1, teacher2, ...]
        $classCache = [];
        $courseCache = [];

        // Initialize counting variables for tracking processed records
        $schoolsProcessedCount = 0;
        $staffProcessedCount = 0;
        $studentsProcessedCount = 0;
        $coursesProcessedCount = 0;
        $classesProcessedCount = 0;
        $enrollmentsProcessedCount = 0;
        $academicSessionsProcessedCount = 0;
        
        // Initialize debug logging
        error_log("=== STARTING ONEROSTER API IMPORT ===");
        error_log("Site ID: $siteID");
        error_log("School Year ID: $schoolYearID");
        error_log("Job Start Time: $jobStartTime");
        error_log("Initial Memory Usage: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB");
        error_log("=====================================");

        // Initialize Google Cloud Storage once
        $storage = new StorageClient(['projectId' => "abre-production"]);
        $bucketName = "prd-landing-zone";
        $bucket = $storage->bucket($bucketName);
        $currentDate = date("Ymd");

        $api = OneRosterAPI::init(
            $config->oneRoster->service,
            $config->oneRoster->clientID,
            $config->oneRoster->clientSecret,
            $config->oneRoster->baseUri,
            $config->oneRoster->authUri
        );

        if ($api === null) {
            $error = "Failed to initialize OneRoster API - check credentials and endpoints";
            error_log("FATAL: " . $error);
            error_log("DEBUG: API Configuration - Service: " . $config->oneRoster->service);
            error_log("DEBUG: API Configuration - Base URI: " . $config->oneRoster->baseUri);
            error_log("DEBUG: API Configuration - Auth URI: " . $config->oneRoster->authUri);
            error_log("DEBUG: API Configuration - Client ID: " . substr($config->oneRoster->clientID, 0, 10) . "...");
            throw new Exception($error);
        } else {
            error_log("DEBUG: OneRoster API initialized successfully");
        }

        // Retry helper: on failure, re-initialize token and retry with proper OAuth error handling
        $apiGet = function ($endpoint, $params = []) use (&$api, $config)
        {
            $maxRetries = 2;
            
            for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
                try {
                    if ($api === null) {
                        error_log("OneRoster: API is null, initializing (attempt $attempt/$maxRetries)");
                        $api = OneRosterAPI::init(
                            $config->oneRoster->service,
                            $config->oneRoster->clientID,
                            $config->oneRoster->clientSecret,
                            $config->oneRoster->baseUri,
                            $config->oneRoster->authUri
                        );
                        
                        if ($api === null) {
                            error_log("OneRoster: Failed to initialize API (attempt $attempt/$maxRetries)");
                            if ($attempt < $maxRetries) {
                                sleep(2);
                            }
                            continue;
                        }
                    }
                    
                    $result = $api->get($endpoint, $params);
                    if ($result !== false) {
                        return $result;
                    }
                    
                    error_log("OneRoster: GET failed for endpoint: $endpoint (attempt $attempt/$maxRetries)");
                    
                } catch (Exception $e) {
                    $errorMessage = $e->getMessage();
                    error_log("OneRoster: API exception for $endpoint (attempt $attempt/$maxRetries): $errorMessage");
                    
                    // Check for OAuth token expiration (401 Unauthorized)
                    if (strpos($errorMessage, '401') !== false || 
                        strpos($errorMessage, 'Unauthorized') !== false ||
                        strpos($errorMessage, 'Expired OAuth token') !== false) {
                        error_log("OneRoster: OAuth token expired, re-authenticating...");
                    }
                    
                    // Check for 404 Not Found (user doesn't exist)
                    if (strpos($errorMessage, '404') !== false || 
                        strpos($errorMessage, 'Not Found') !== false) {
                        error_log("OneRoster: Resource not found (404) for endpoint: $endpoint - skipping");
                        return false; // Don't retry 404 errors
                    }
                }
                
                // Re-initialize API for next attempt (refresh token)
                if ($attempt < $maxRetries) {
                    error_log("OneRoster: Re-initializing API for retry...");
                    $api = OneRosterAPI::init(
                        $config->oneRoster->service,
                        $config->oneRoster->clientID,
                        $config->oneRoster->clientSecret,
                        $config->oneRoster->baseUri,
                        $config->oneRoster->authUri
                    );
                    
                    if ($api === null) {
                        error_log("OneRoster: Failed to re-initialize API for retry");
                    }
                    
                    sleep(1);
                }
            }
            
            error_log("OneRoster: All retry attempts failed for endpoint: $endpoint");
            return false;
        };

        error_log("starting schools");
        $limit = 1000;
        $offset = 0;
        $valuesToImport = [];
        $dbColumns = "";
        $schoolsStream = _initGcsStream('schools', $bucket, $currentDate, $siteID);
        
        if ($schoolsStream === false) {
            error_log("FATAL: Failed to initialize schools GCS stream - aborting schools processing");
            throw new Exception("Failed to initialize schools GCS stream");
        }

        try {
            do {
                $schools = $apiGet("ims/oneroster/v1p1/schools", [
                    "offset" => $offset,
                    "limit" => $limit
                ]);

                if ($schools !== false && isset($schools["orgs"]) && is_array($schools["orgs"]) && count($schools["orgs"])) {
                    error_log("DEBUG: Processing " . count($schools["orgs"]) . " schools from API (offset: $offset)");
                    error_log("DEBUG: Schools API response structure validated successfully for offset: $offset");

                    if ($dbColumns == "") {
                        $dbColumns = "INSERT INTO abre_schools (code, name, site_id, school_year_id) VALUES ";
                    }

                    foreach ($schools["orgs"] as $school) {
                        $schoolsProcessedCount++;
                        $code = $school["identifier"] != "" ? $school["identifier"] : $school["sourcedId"];
                        $codeEscaped = trim($db->escape_string($code));

                        $nameEscaped = trim($db->escape_string($school["name"]));

                        $valuesToImport[] = "(
                  '$codeEscaped', '$nameEscaped', $siteID, $schoolYearID
                )";

                        if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                            error_log("DEBUG: Inserting batch of " . MAX_IMPORT_LIMIT . " schools to database (total processed so far: $schoolsProcessedCount)");
                            insertRows($db, $dbColumns, $valuesToImport, "ON DUPLICATE KEY UPDATE abre_schools.code = VALUES(code), abre_schools.name = VALUES(name), abre_schools.site_id = VALUES(site_id)");
                            $valuesToImport = [];
                        }

                        $schoolsCache[$school["sourcedId"]] = [
                            "id" => $code,
                            "name" => $school["name"],
                            "type" => $school["type"]
                        ];

                        // Stream schools to GCS
                        if ($schoolsStream !== false) {
                            _appendToGcsStream($schoolsStream, $school);
                        }
                    }
                } else {
                    if ($schools === false) {
                        error_log("ERROR: Schools API call failed - returned false (offset: $offset)");
                        error_log("DEBUG: Schools API endpoint: ims/oneroster/v1p1/schools");
                    } elseif (!isset($schools["orgs"])) {
                        error_log("ERROR: Schools API response missing 'orgs' field (offset: $offset)");
                        error_log("DEBUG: Schools API response keys: " . (is_array($schools) ? implode(', ', array_keys($schools)) : 'not an array'));
                    } elseif (!is_array($schools["orgs"])) {
                        error_log("ERROR: Schools API response 'orgs' field is not an array (offset: $offset)");
                        error_log("DEBUG: Schools 'orgs' field type: " . gettype($schools["orgs"]));
                    } else {
                        error_log("INFO: Schools API returned empty orgs array (offset: $offset)");
                    }
                }
                $offset += $limit;
            } while (isset($schools["orgs"]) && count($schools["orgs"]));
            
            if (count($valuesToImport)) {
                error_log("DEBUG: Inserting final batch of " . count($valuesToImport) . " schools to database (total processed: $schoolsProcessedCount)");
                insertRows($db, $dbColumns, $valuesToImport, "ON DUPLICATE KEY UPDATE abre_schools.code = VALUES(code), abre_schools.name = VALUES(name), abre_schools.site_id = VALUES(site_id)");
            }
        } finally {
            // Ensure stream is always finalized even if exceptions occur
            if ($schoolsStream !== false) {
                _finalizeGcsStream($schoolsStream);
            }
            $schools = null;
            unset($schoolsStream);
            gc_collect_cycles();
        }
        error_log("done with schools - processed: $schoolsProcessedCount schools");
        error_log("Memory usage after schools: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB");

        error_log("starting academic sessions");
        $limit = 1000;
        $offset = 0;
        $valuesToImport = [];
        $termColumns = "";
        $termDefinitions = [];
        $deleteExecuted = false;
        $academicSessionsStream = _initGcsStream('academicSessions', $bucket, $currentDate, $siteID);
        
        if ($academicSessionsStream === false) {
            error_log("FATAL: Failed to initialize academic sessions GCS stream - aborting academic sessions processing");
            throw new Exception("Failed to initialize academic sessions GCS stream");
        }

        try {
            do {
                $academicSessions = $apiGet("ims/oneroster/v1p1/academicSessions", [
                    "offset" => $offset,
                    "limit" => $limit
                ]);

                if ($academicSessions !== false && isset($academicSessions["academicSessions"]) && is_array($academicSessions["academicSessions"]) && count($academicSessions["academicSessions"])) {
                    error_log("DEBUG: Processing " . count($academicSessions["academicSessions"]) . " academic sessions from API (offset: $offset)");
                    error_log("DEBUG: Academic sessions API response structure validated successfully for offset: $offset");
                    
                    // Only delete once on first iteration
                    if (!$deleteExecuted) {
                        $deleteSql = "DELETE FROM abre_term WHERE site_id = ? AND term_year_id = ? AND is_imported = 1";
                        $deleteStmt = $db->stmt_init();
                        if (!$deleteStmt->prepare($deleteSql)) {
                            error_log("Failed to prepare delete statement for abre_term: " . $db->error);
                            throw new Exception("Database prepare error: " . $db->error);
                        }
                        $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                        if (!$deleteStmt->execute()) {
                            error_log("Failed to execute delete statement for abre_term: " . $deleteStmt->error);
                            $deleteStmt->close();
                            throw new Exception("Database execute error: " . $deleteStmt->error);
                        }
                        $deleteStmt->close();
                        $deleteExecuted = true;
                    }

                    if ($termColumns == "") {
                        $termColumns = "INSERT INTO abre_term
                                    (site_id, term_definition_id, term_year_id, start_date, end_date, is_imported)
                                  VALUES ";
                    }

                    foreach ($academicSessions["academicSessions"] as $session) {
                        $academicSessionsProcessedCount++;
                        // Stream academic sessions to GCS
                        if ($academicSessionsStream !== false) {
                            _appendToGcsStream($academicSessionsStream, $session);
                        }

                        // $session["schoolYear"] == $currentEndingSchoolYear
                        //   &&
                        if ($session["type"] != "schoolYear") {

                            $customSourceID = $session["sourcedId"] . "-$siteID";
                            if (!isset($termDefinitions[$customSourceID])) {
                                $existingDefinitionID = null;
                                $sessionSelectSql = "SELECT id FROM abre_term_definition WHERE term_input = ?";
                                $sessionSelectStmt = $db->stmt_init();
                                if (!$sessionSelectStmt->prepare($sessionSelectSql)) {
                                    error_log("Failed to prepare select statement for abre_term_definition: " . $db->error);
                                    throw new Exception("Database prepare error: " . $db->error);
                                }
                                $sessionSelectStmt->bind_param("s", $customSourceID);
                                if (!$sessionSelectStmt->execute()) {
                                    error_log("Failed to execute select statement for abre_term_definition: " . $sessionSelectStmt->error);
                                    $sessionSelectStmt->close();
                                    throw new Exception("Database execute error: " . $sessionSelectStmt->error);
                                }
                                $sessionSelectStmt->bind_result($existingDefinitionID);
                                $sessionSelectStmt->fetch();
                                $sessionSelectStmt->close();

                                if ($existingDefinitionID === null) {
                                    $insertTerm = "INSERT INTO abre_term_definition (term_input, display_short, display_long, is_hidden)
                                      VALUES (?, ?, ?, 0)";
                                    $insertStmt = $db->stmt_init();
                                    if (!$insertStmt->prepare($insertTerm)) {
                                        error_log("Failed to prepare insert statement for abre_term_definition: " . $db->error);
                                        throw new Exception("Database prepare error: " . $db->error);
                                    }
                                    $insertStmt->bind_param("sss", $customSourceID, $session["title"], $session["title"]);
                                    if (!$insertStmt->execute()) {
                                        error_log("Failed to execute insert statement for abre_term_definition: " . $insertStmt->error);
                                        $insertStmt->close();
                                        throw new Exception("Database execute error: " . $insertStmt->error);
                                    }
                                    $newTermID = $insertStmt->insert_id;
                                    $insertStmt->close();
                                    $termDefinitions[$customSourceID] = $newTermID;
                                } else {
                                    $termDefinitions[$customSourceID] = $existingDefinitionID;
                                }
                            }

                            $escapedTermID = trim($db->escape_string($termDefinitions[$customSourceID]));
                            $escapedStartDate = trim($db->escape_string($session["startDate"]));
                            $escapedEndDate = trim($db->escape_string($session["endDate"]));
                            $valuesToImport[] = "(
                    $siteID, $escapedTermID, $schoolYearID, '$escapedStartDate', '$escapedEndDate', 1
                  )";

                            if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                                // Inserting academic sessions batch
                                insertRows($db, $termColumns, $valuesToImport);
                                $valuesToImport = [];
                            }

                            $gradingPeriods[$customSourceID] = [
                                "children" => isset($session["children"]) ? $session["children"] : []
                            ];
                        }
                    }
                } else {
                    if ($academicSessions === false) {
                        error_log("ERROR: Academic sessions API call failed - returned false (offset: $offset)");
                        error_log("DEBUG: Academic sessions API endpoint: ims/oneroster/v1p1/academicSessions");
                    } elseif (!isset($academicSessions["academicSessions"])) {
                        error_log("ERROR: Academic sessions API response missing 'academicSessions' field (offset: $offset)");
                        error_log("DEBUG: Academic sessions API response keys: " . (is_array($academicSessions) ? implode(', ', array_keys($academicSessions)) : 'not an array'));
                    } elseif (!is_array($academicSessions["academicSessions"])) {
                        error_log("ERROR: Academic sessions API response 'academicSessions' field is not an array (offset: $offset)");
                        error_log("DEBUG: Academic sessions 'academicSessions' field type: " . gettype($academicSessions["academicSessions"]));
                    } else {
                        error_log("INFO: Academic sessions API returned empty academicSessions array (offset: $offset)");
                    }
                }
                $offset += $limit;
            } while (isset($academicSessions["academicSessions"]) && count($academicSessions["academicSessions"]));
            
            if (count($valuesToImport)) {
                // Inserting final academic sessions batch
                insertRows($db, $termColumns, $valuesToImport);
            }
        } finally {
            // Ensure stream is always finalized even if exceptions occur
            if ($academicSessionsStream !== false) {
                _finalizeGcsStream($academicSessionsStream);
            }
            $academicSessions = null;
            unset($academicSessionsStream);
            gc_collect_cycles();
        }
        error_log("done with academic sessions - processed: $academicSessionsProcessedCount academic sessions");
        error_log("Memory usage after academic sessions: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB");

        error_log("starting students");
        $limit = 1000;
        $offset = 0;
        $deleteRecords = true;
        $valuesToImport = [];
        $emailsToImport = [];
        $dbColumns = "INSERT INTO Abre_Students
                  (StudentId, FirstName, MiddleName, LastName, Email, SSID, SchoolCode,
                    SchoolName, CurrentGrade, okay_to_publish, siteID, school_year_id)
                  VALUES";
        $adColumns = "INSERT INTO Abre_AD (Email, StudentID, siteID, school_year_id) VALUES";
        $studentsStream = _initGcsStream('students', $bucket, $currentDate, $siteID);
        
        if ($studentsStream === false) {
            error_log("FATAL: Failed to initialize students GCS stream - aborting students processing");
            throw new Exception("Failed to initialize students GCS stream");
        }

        try {
        do {
            $students = $apiGet("ims/oneroster/v1p1/students", [
                "offset" => $offset,
                "limit" => $limit
            ]);
            if ($students !== false && isset($students["users"]) && is_array($students["users"]) && count($students["users"])) {
                error_log("DEBUG: Processing " . count($students["users"]) . " students from API (offset: $offset)");
                error_log("DEBUG: Students API response structure validated successfully for offset: $offset");
                if ($deleteRecords) {
                    $deleteSql = "DELETE FROM Abre_Students WHERE siteID = ? AND school_year_id = ?";
                    $deleteStmt = $db->stmt_init();
                    if (!$deleteStmt->prepare($deleteSql)) {
                        error_log("Failed to prepare delete statement for Abre_Students: " . $db->error);
                        throw new Exception("Database prepare error: " . $db->error);
                    }
                    $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                    if (!$deleteStmt->execute()) {
                        error_log("Failed to execute delete statement for Abre_Students: " . $deleteStmt->error);
                        $deleteStmt->close();
                        throw new Exception("Database execute error: " . $deleteStmt->error);
                    }
                    $deleteStmt->close();

                    $deleteSql = "DELETE FROM Abre_AD WHERE siteID = ? AND school_year_id = ?";
                    $deleteStmt = $db->stmt_init();
                    if (!$deleteStmt->prepare($deleteSql)) {
                        error_log("Failed to prepare delete statement for Abre_AD: " . $db->error);
                        throw new Exception("Database prepare error: " . $db->error);
                    }
                    $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                    if (!$deleteStmt->execute()) {
                        error_log("Failed to execute delete statement for Abre_AD: " . $deleteStmt->error);
                        $deleteStmt->close();
                        throw new Exception("Database execute error: " . $deleteStmt->error);
                    }
                    $deleteStmt->close();

                    $deleteRecords = false;
                }

                foreach ($students["users"] as $student) {
                    if ($student["enabledUser"] && $student["status"] == "active") {
                        $studentsProcessedCount++;

                        if (isset($config->oneRoster->studentId)) {
                            if ($config->oneRoster->studentId === 'sourcedId') {
                                $studentID = $student["sourcedId"];
                            } elseif ($config->oneRoster->studentId === 'identifier') {
                                $studentID = $student["identifier"];
                            } else {
                                // Fallback in case it's set to something unexpected
                                $studentID = $student["identifier"] != "" ? $student["identifier"] : $student["sourcedId"];
                            }
                        } else {
                            $studentID = $student["identifier"] != "" ? $student["identifier"] : $student["sourcedId"];
                        }
                        $studentID = trim($db->escape_string($studentID));

                        $stateId = '';
                        if (isset($student['userIds'])) {
                            foreach ($student['userIds'] as $userId) {
                                if ($userId['type'] === 'stateId') {
                                    $stateId = trim($db->escape_string($userId['identifier']));
                                    break;
                                }
                            }
                        }

                        // Enhanced validation for student data fields
                        $firstName = isset($student["givenName"]) ? trim($db->escape_string($student["givenName"])) : "";
                        $middleName = isset($student["middleName"]) ? trim($db->escape_string($student["middleName"])) : "";
                        $lastName = isset($student["familyName"]) ? trim($db->escape_string($student["familyName"])) : "";
                        $email = isset($student["email"]) ? trim($db->escape_string($student["email"])) : "";
                        
                        // Validate grades array exists and has at least one element
                        $currentGrade = "";
                        if (isset($student["grades"]) && is_array($student["grades"]) && count($student["grades"]) > 0) {
                            $currentGrade = trim($db->escape_string($student["grades"][0]));
                        } else {
                            error_log("WARNING: Student '{$studentID}' has missing or invalid grades data");
                        }

                        $emailsToImport[] = "('$email', '$studentID', $siteID, $schoolYearID)";
                        if (count($emailsToImport) == MAX_IMPORT_LIMIT) {
                            error_log("DEBUG: Inserting batch of " . MAX_IMPORT_LIMIT . " student emails to database (total students processed so far: $studentsProcessedCount)");
                            insertRows($db, $adColumns, $emailsToImport);
                            $emailsToImport = [];
                        }

                        // Enhanced validation for student organizations array
                        if (!isset($student["orgs"]) || !is_array($student["orgs"]) || count($student["orgs"]) === 0) {
                            error_log("WARNING: Student '{$studentID}' has missing or empty organizations array - skipping school assignments");
                        } else {
                            foreach ($student["orgs"] as $building) {
                            // Enhanced school cache validation with defensive programming
                            if (!isset($building["sourcedId"]) || empty($building["sourcedId"])) {
                                error_log("WARNING: Student '{$studentID}' has building with missing or empty sourcedId");
                                continue;
                            }
                            
                            if (!isset($schoolsCache[$building["sourcedId"]])) {
                                error_log("WARNING: School sourcedId '{$building["sourcedId"]}' not found in schoolsCache for student '{$studentID}' - skipping this school assignment");
                                continue;
                            }
                            
                            $schoolData = $schoolsCache[$building["sourcedId"]];
                            if (!isset($schoolData["id"]) || !isset($schoolData["name"]) || !isset($schoolData["type"])) {
                                error_log("ERROR: Incomplete school data in cache for sourcedId '{$building["sourcedId"]}' - missing required fields");
                                continue;
                            }
                            
                            $schoolCode = $schoolData["id"];
                            $schoolCode = trim($db->escape_string($schoolCode));

                            $buildingName = trim($db->escape_string($schoolData["name"]));
                            $buildingType = trim($db->escape_string($schoolData["type"]));

                            // Check if Building a School and if so add it
                            if ($buildingType == "school") {
                                $valuesToImport[] = "(
                  '$studentID', '$firstName', '$middleName', '$lastName', '$email', '$stateId', '$schoolCode',
                  '$buildingName', '$currentGrade', 1, $siteID, $schoolYearID
                )";
                            }

                            if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                                error_log("DEBUG: Inserting batch of " . MAX_IMPORT_LIMIT . " students to database (total students processed so far: $studentsProcessedCount)");
                                insertRows($db, $dbColumns, $valuesToImport);
                                $valuesToImport = [];
                            }
                        }
                        }

                        // Enhanced validation for usersCache assignment
                        if (isset($student["sourcedId"]) && !empty($student["sourcedId"])) {
                            $usersCache[$student["sourcedId"]] = [
                                "localId" => $studentID,
                                "firstName" => isset($student["givenName"]) ? $student["givenName"] : "",
                                "middleName" => isset($student["middleName"]) ? $student["middleName"] : "",
                                "lastName" => isset($student["familyName"]) ? $student["familyName"] : ""
                            ];
                        } else {
                            error_log("WARNING: Student has missing or empty sourcedId - cannot add to usersCache");
                        }

                        // Stream students to GCS
                        if ($studentsStream !== false) {
                            _appendToGcsStream($studentsStream, $student);
                        }
                    }
                }
            } else {
                if ($students === false) {
                    error_log("ERROR: Students API call failed - returned false (offset: $offset)");
                    error_log("DEBUG: Students API endpoint: ims/oneroster/v1p1/students");
                } elseif (!isset($students["users"])) {
                    error_log("ERROR: Students API response missing 'users' field (offset: $offset)");
                    error_log("DEBUG: Students API response keys: " . (is_array($students) ? implode(', ', array_keys($students)) : 'not an array'));
                } elseif (!is_array($students["users"])) {
                    error_log("ERROR: Students API response 'users' field is not an array (offset: $offset)");
                    error_log("DEBUG: Students 'users' field type: " . gettype($students["users"]));
                } else {
                    error_log("INFO: Students API returned empty users array (offset: $offset)");
                }
            }
            $offset += $limit;

            // Periodic memory cleanup and monitoring for large student datasets
            if ($offset % 5000 === 0) {
                gc_collect_cycles();
                $memoryUsageMB = memory_get_usage(true) / 1024 / 1024;
                $peakMemoryMB = memory_get_peak_usage(true) / 1024 / 1024;
                error_log("DEBUG: Processed $offset students (total students processed: $studentsProcessedCount), current memory: {$memoryUsageMB}MB, peak memory: {$peakMemoryMB}MB");
            }
        } while (isset($students["users"]) && count($students["users"]));
        if (count($valuesToImport)) {
            error_log("DEBUG: Inserting final batch of " . count($valuesToImport) . " students to database (total processed: $studentsProcessedCount)");
            insertRows($db, $dbColumns, $valuesToImport);
        }
        if (count($emailsToImport)) {
            error_log("DEBUG: Inserting final batch of " . count($emailsToImport) . " student emails to database (total processed: $studentsProcessedCount)");
            insertRows($db, $adColumns, $emailsToImport);
        }

        } finally {
            // Ensure stream is always finalized even if exceptions occur
            if ($studentsStream !== false) {
                _finalizeGcsStream($studentsStream);
            }
            $students = null;
            unset($studentsStream);
            gc_collect_cycles();
        }
        error_log("done with students - processed: $studentsProcessedCount students");
        error_log("Memory usage after students: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB");

        error_log("starting staff");
        $limit = 1000;
        $offset = 0;
        $valuesToImport = [];
        $dbColumns = "INSERT INTO Abre_Staff
              (StaffID, FirstName, MiddleName, LastName, EMail1,
                is_imported, imported_on, siteID, school_year_id)
              VALUES";

        $emptyStringEncrypted = isset($config->dbKey) ? encrypt("", $config->dbKey->iv, $config->dbKey->key) : "";
        $insertSql = "INSERT INTO directory
                  (updatedtime, superadmin, admin, picture, firstname, lastname,
                    middlename, address, city, state, zip, email, phone, extension,
                    cellphone, ss, dob, gender, ethnicity, title, contract, classification,
                    location, grade, subject, doh, senioritydate, effectivedate, rategroup,
                    step, educationlevel, salary, hours, probationreportdate,
                    statebackgroundcheck, federalbackgroundcheck, stateeducatorid,
                    licensetype1, licenseissuedate1, licenseexpirationdate1, licenseterm1,
                    licensetype2, licenseissuedate2, licenseexpirationdate2, licenseterm2,
                    licensetype3, licenseissuedate3, licenseexpirationdate3, licenseterm3,
                    licensetype4, licenseissuedate4, licenseexpirationdate4, licenseterm4,
                    licensetype5, licenseissuedate5, licenseexpirationdate5, licenseterm5,
                    licensetype6, licenseissuedate6, licenseexpirationdate6, licenseterm6,
                    permissions, role, contractdays, siteID
                  )
                  VALUES (CURRENT_TIMESTAMP, 0, 0, '', ?, ?, '', ?, ?, ?, ?, ?, ?, '',
                    ?, ?, ?, ?, ?, '', ?, '', '', '', '', ?, ?, ?, ?, ?, ?, ?, ?,
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                  );";
        $insertStmt = $db->stmt_init();
        $insertStmt->prepare($insertSql);
        $staffStream = _initGcsStream('staff', $bucket, $currentDate, $siteID);
        
        if ($staffStream === false) {
            error_log("FATAL: Failed to initialize staff GCS stream - aborting staff processing");
            throw new Exception("Failed to initialize staff GCS stream");
        }

        try {
        do {
            $users = $apiGet("ims/oneroster/v1p1/users", [
                "offset" => $offset,
                "limit" => $limit
            ]);
            if ($users !== false && isset($users["users"]) && is_array($users["users"]) && count($users["users"])) {
                error_log("DEBUG: Processing " . count($users["users"]) . " users from API (offset: $offset)");
                error_log("DEBUG: Staff/Users API response structure validated successfully for offset: $offset");
                foreach ($users["users"] as $user) {
                    if ($user["role"] == "teacher") {
                        $staffProcessedCount++;
                        
                        // Enhanced validation for staff data fields
                        if (!isset($user["identifier"]) && !isset($user["sourcedId"])) {
                            error_log("ERROR: Staff user missing both identifier and sourcedId - skipping");
                            continue;
                        }
                        
                        $staffID = $user["identifier"] != "" ? $user["identifier"] : $user["sourcedId"];
                        $escapedStaffID = trim($db->escape_string($staffID));
                        
                        // Enhanced validation for required staff fields
                        if (!isset($user["givenName"]) || !isset($user["familyName"])) {
                            error_log("WARNING: Staff '{$staffID}' missing required name fields - using empty strings");
                        }
                        if (!isset($user["email"]) || empty($user["email"])) {
                            error_log("WARNING: Staff '{$staffID}' missing or empty email field");
                        }

                        // Enhanced field validation with defensive programming
                        $firstName = isset($user["givenName"]) ? trim($db->escape_string($user["givenName"])) : "";
                        $middleName = isset($user["middleName"]) ? trim($db->escape_string($user["middleName"])) : "";
                        $lastName = isset($user["familyName"]) ? trim($db->escape_string($user["familyName"])) : "";
                        $email = isset($user["email"]) ? trim($db->escape_string($user["email"])) : "";

                        $valuesToImport[] = "(
              '$escapedStaffID', '$firstName', '$middleName', '$lastName', '$email', 1, NOW(), $siteID, $schoolYearID
            )";

                        if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                            error_log("DEBUG: Inserting batch of " . MAX_IMPORT_LIMIT . " staff to database (total processed so far: $staffProcessedCount)");
                            $values = implode(",", $valuesToImport);
                            $importQuery = "$dbColumns $values ON DUPLICATE KEY UPDATE
                              FirstName = VALUES(FirstName),
                              MiddleName = VALUES(MiddleName),
                              LastName = VALUES(LastName),
                              EMail1 = VALUES(EMail1),
                              is_imported = 1,
                              imported_on = NOW(),
                              is_archived = 0";
                            $db->query($importQuery);
                            $valuesToImport = [];
                        }

                        $quotedSchools = array_map(function ($school) use ($schoolsCache) {
                            $schoolID = $schoolsCache[$school["sourcedId"]]["id"];
                            return "'$schoolID'";
                        }, $user["orgs"]);
                        $schoolsForSql = "(" . join(",", $quotedSchools) . ")";

                        $schoolDeleteSql = "DELETE FROM abre_staff_schools
                                WHERE school_code NOT IN $schoolsForSql
                                  AND is_imported = 1 AND staff_id = ? AND site_id = ?";
                        $deleteSchoolsStmt = $db->stmt_init();
                        $deleteSchoolsStmt->prepare($schoolDeleteSql);
                        $deleteSchoolsStmt->bind_param("si", $staffID, $siteID);
                        $deleteSchoolsStmt->execute();
                        $deleteSchoolsStmt->close();

                        $schoolsToImport = [];
                        foreach ($user["orgs"] as $school) {
                            $schoolID = $schoolsCache[$school["sourcedId"]]["id"];
                            $schoolsToImport[] = "('$escapedStaffID', '$schoolID', $siteID, 1, NOW())";
                        }
                        $schoolsImportString = implode(",", $schoolsToImport);

                        $schoolInsertSql = "INSERT INTO abre_staff_schools
                                  (staff_id, school_code, site_id, is_imported, imported_on)
                                VALUES $schoolsImportString
                                ON DUPLICATE KEY UPDATE
                                  is_imported = 1,
                                  imported_on = NOW()";
                        $insertSchoolStmt = $db->stmt_init();
                        $insertSchoolStmt->prepare($schoolInsertSql);
                        $insertSchoolStmt->execute();
                        $insertSchoolStmt->close();

                        if ($user["email"] != "") {
                            $selectSql = "SELECT COUNT(*) FROM directory WHERE email = ? AND siteID = ?";
                            $selectStmt = $db->stmt_init();
                            $selectStmt->prepare($selectSql);
                            $selectStmt->bind_param("si", $user["email"], $siteID);
                            $selectStmt->execute();
                            $existingRow = 0;
                            $selectStmt->bind_result($existingRow);
                            $selectStmt->fetch();
                            $selectStmt->close();

                            if (!$existingRow) {
                                $insertStmt->bind_param(
                                    "sssssssssssssssssssssssssssssssssssssssssssssssssssssi",
                                    $user["givenName"],
                                    $user["familyName"],
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $user["email"],
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $siteID
                                );
                                $insertStmt->execute();
                            }
                        }

                        // Enhanced validation for usersCache assignment
                        if (isset($user["sourcedId"]) && !empty($user["sourcedId"])) {
                            $usersCache[$user["sourcedId"]] = [
                                "localId" => $staffID,
                                "firstName" => isset($user["givenName"]) ? $user["givenName"] : "",
                                "middleName" => isset($user["middleName"]) ? $user["middleName"] : "",
                                "lastName" => isset($user["familyName"]) ? $user["familyName"] : ""
                            ];
                        } else {
                            error_log("WARNING: Staff has missing or empty sourcedId - cannot add to usersCache");
                        }

                        // Stream staff to GCS
                        if ($staffStream !== false) {
                            _appendToGcsStream($staffStream, $user);
                        }

                        // Enhanced validation for teacher classes API call
                        if (isset($user["sourcedId"]) && !empty($user["sourcedId"])) {
                            $teacherClasses = $apiGet("ims/oneroster/v1p1/teachers/" . $user["sourcedId"] . "/classes");
                            if ($teacherClasses !== false && isset($teacherClasses["classes"]) && is_array($teacherClasses["classes"])) {
                                error_log("DEBUG: Processing " . count($teacherClasses["classes"]) . " classes for staff '{$staffID}'");
                                foreach ($teacherClasses["classes"] as $class) {
                                    if (!isset($class["sourcedId"]) || empty($class["sourcedId"])) {
                                        error_log("WARNING: Class missing sourcedId for staff '{$staffID}' - skipping");
                                        continue;
                                    }
                                    
                                    $classSourcedId = $class["sourcedId"];

                                    // Build O(1) teacher lookup index
                                    if (!isset($classTeachersIndex[$classSourcedId])) {
                                        $classTeachersIndex[$classSourcedId] = [];
                                    }

                                    $classTeachersIndex[$classSourcedId][] = [
                                        "localId" => $staffID,
                                        "firstName" => isset($user["givenName"]) ? $user["givenName"] : "",
                                        "middleName" => isset($user["middleName"]) ? $user["middleName"] : "",
                                        "lastName" => isset($user["familyName"]) ? $user["familyName"] : ""
                                    ];

                                    if (!isset($teacherCache[$classSourcedId])) {
                                        $teacherCache[$classSourcedId] = [
                                            "localId" => $staffID,
                                            "firstName" => isset($user["givenName"]) ? $user["givenName"] : "",
                                            "middleName" => isset($user["middleName"]) ? $user["middleName"] : "",
                                            "lastName" => isset($user["familyName"]) ? $user["familyName"] : ""
                                        ];
                                    }
                                }
                            } else {
                                if ($teacherClasses === false) {
                                    error_log("WARNING: Teacher classes API call failed for staff '{$staffID}'");
                                } elseif (!isset($teacherClasses["classes"])) {
                                    error_log("WARNING: Teacher classes API response missing 'classes' field for staff '{$staffID}'");
                                } elseif (!is_array($teacherClasses["classes"])) {
                                    error_log("WARNING: Teacher classes API response 'classes' field is not an array for staff '{$staffID}'");
                                } else {
                                    error_log("INFO: No classes found for staff '{$staffID}'");
                                }
                            }
                        } else {
                            error_log("WARNING: Cannot fetch teacher classes - staff missing sourcedId");
                        }
                    }
                }
            } else {
                if ($users === false) {
                    error_log("ERROR: Staff/Users API call failed - returned false (offset: $offset)");
                    error_log("DEBUG: Staff/Users API endpoint: ims/oneroster/v1p1/users");
                } elseif (!isset($users["users"])) {
                    error_log("ERROR: Staff/Users API response missing 'users' field (offset: $offset)");
                    error_log("DEBUG: Staff/Users API response keys: " . (is_array($users) ? implode(', ', array_keys($users)) : 'not an array'));
                } elseif (!is_array($users["users"])) {
                    error_log("ERROR: Staff/Users API response 'users' field is not an array (offset: $offset)");
                    error_log("DEBUG: Staff/Users 'users' field type: " . gettype($users["users"]));
                } else {
                    error_log("INFO: Staff/Users API returned empty users array (offset: $offset)");
                }
            }
            $offset += $limit;

            // Periodic memory cleanup for large staff datasets
            if ($offset % 5000 === 0) {
                gc_collect_cycles();
                error_log("Processed $offset staff, memory usage: " . memory_get_usage(true) / 1024 / 1024 . " MB");
            }
        } while (isset($users["users"]) && count($users["users"]));
        $insertStmt->close();

        if (count($valuesToImport)) {
            error_log("DEBUG: Inserting final batch of " . count($valuesToImport) . " staff to database (total processed: $staffProcessedCount)");
            $values = implode(",", $valuesToImport);
            $importQuery = "$dbColumns $values ON DUPLICATE KEY UPDATE
                      FirstName = VALUES(FirstName),
                      MiddleName = VALUES(MiddleName),
                      LastName = VALUES(LastName),
                      EMail1 = VALUES(EMail1),
                      is_imported = 1,
                      imported_on = NOW(),
                      is_archived = 0";
            $db->query($importQuery);
            $valuesToImport = [];
        }

        // Remove building relationships for staff no longer in import file
        $deleteOldBuildings = "DELETE FROM abre_staff_schools
                           WHERE site_id = ? AND is_imported = 1
                             AND imported_on < ?";
        $deleteOldStmt = $db->stmt_init();
        if (!$deleteOldStmt->prepare($deleteOldBuildings)) {
            error_log("Failed to prepare delete statement for abre_staff_schools cleanup: " . $db->error);
            throw new Exception("Database prepare error: " . $db->error);
        }
        $deleteOldStmt->bind_param("is", $siteID, $jobStartTime);
        if (!$deleteOldStmt->execute()) {
            error_log("Failed to execute delete statement for abre_staff_schools cleanup: " . $deleteOldStmt->error);
            $deleteOldStmt->close();
            throw new Exception("Database execute error: " . $deleteOldStmt->error);
        }
        $deleteOldStmt->close();

        // Archive staff that are no longer in the import file
        $archiveOldStaff = "UPDATE Abre_Staff SET is_archived = 1
                        WHERE siteID = ? AND is_imported = 1
                          AND imported_on < ? AND school_year_id = ?";
        $archiveStmt = $db->stmt_init();
        if (!$archiveStmt->prepare($archiveOldStaff)) {
            error_log("Failed to prepare update statement for Abre_Staff archive: " . $db->error);
            throw new Exception("Database prepare error: " . $db->error);
        }
        $archiveStmt->bind_param("isi", $siteID, $jobStartTime, $schoolYearID);
        if (!$archiveStmt->execute()) {
            error_log("Failed to execute update statement for Abre_Staff archive: " . $archiveStmt->error);
            $archiveStmt->close();
            throw new Exception("Database execute error: " . $archiveStmt->error);
        }
        $archiveStmt->close();

        // Ensure the archive flags in the staff table are consistent with the import
        // Flags in the directory
        $directoryArchiveUpdate = "UPDATE directory d
                                JOIN (
                                  SELECT EMail1, siteID, MIN(is_archived) isArchived
                                    FROM Abre_Staff
                                  WHERE siteID = ? AND is_imported = 1 AND school_year_id = ?
                                  GROUP BY EMail1, siteID
                                ) staff
                                ON staff.EMail1 = d.email AND staff.siteID = d.siteID
                                SET d.archived = staff.isArchived";
        $directoryArchiveStmt = $db->stmt_init();
        if (!$directoryArchiveStmt->prepare($directoryArchiveUpdate)) {
            error_log("Failed to prepare update statement for directory archive: " . $db->error);
            throw new Exception("Database prepare error: " . $db->error);
        }
        $directoryArchiveStmt->bind_param("ii", $siteID, $schoolYearID);
        if (!$directoryArchiveStmt->execute()) {
            error_log("Failed to execute update statement for directory archive: " . $directoryArchiveStmt->error);
            $directoryArchiveStmt->close();
            throw new Exception("Database execute error: " . $directoryArchiveStmt->error);
        }
        $directoryArchiveStmt->close();

        } finally {
            // Ensure stream is always finalized even if exceptions occur
            if ($staffStream !== false) {
                _finalizeGcsStream($staffStream);
            }
            $users = null;
            unset($staffStream);
            gc_collect_cycles();
        }
        error_log("done with staff - processed: $staffProcessedCount staff");
        error_log("Memory usage after staff: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB");

        error_log("starting courses");
        $limit = 1000;
        $offset = 0;
        $coursesStream = _initGcsStream('courses', $bucket, $currentDate, $siteID);
        
        if ($coursesStream === false) {
            error_log("FATAL: Failed to initialize courses GCS stream - aborting courses processing");
            throw new Exception("Failed to initialize courses GCS stream");
        }

        try {
        do {
            $courses = $apiGet("ims/oneroster/v1p1/courses", [
                "offset" => $offset,
                "limit" => $limit
            ]);
            if (isset($courses["courses"]) && count($courses["courses"])) {
                foreach ($courses["courses"] as $course) {
                    $coursesProcessedCount++;
                    $courseID = $course["courseCode"] != "" ? $course["courseCode"] : $course["sourcedId"];
                    $courseCache[$course["sourcedId"]] = [
                        "id" => $courseID
                    ];

                    // Stream courses to GCS
                    if ($coursesStream !== false) {
                        _appendToGcsStream($coursesStream, $course);
                    }
                }
            }
            $offset += $limit;
        } while (isset($courses["courses"]) && count($courses["courses"]));

        } finally {
            // Ensure stream is always finalized even if exceptions occur
            if ($coursesStream !== false) {
                _finalizeGcsStream($coursesStream);
            }
            unset($coursesStream);
            gc_collect_cycles();
        }
        error_log("done with courses - processed: $coursesProcessedCount courses");
        error_log("Memory usage after courses: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB");

        error_log("starting classes");
        $limit = 1000;
        $offset = 0;
        $deleteRecords = true;
        $valuesToImport = [];
        $dbColumns = "INSERT INTO Abre_Courses
                    (SchoolCode, CourseCode, SectionCode, StaffID, TermCode, Period, siteID, school_year_id)
                  VALUES";
        $classesStream = _initGcsStream('classes', $bucket, $currentDate, $siteID);
        
        if ($classesStream === false) {
            error_log("FATAL: Failed to initialize classes GCS stream - aborting classes processing");
            throw new Exception("Failed to initialize classes GCS stream");
        }

        try {
        do {
            $classes = $apiGet("ims/oneroster/v1p1/classes", [
                "offset" => $offset,
                "limit" => $limit
            ]);
            if (isset($classes["classes"]) && count($classes["classes"])) {
                if ($deleteRecords) {
                    $deleteSql = "DELETE FROM Abre_Courses WHERE siteID = ? AND school_year_id = ?";
                    $deleteStmt = $db->stmt_init();
                    if (!$deleteStmt->prepare($deleteSql)) {
                        error_log("Failed to prepare delete statement for Abre_Courses: " . $db->error);
                        throw new Exception("Database prepare error: " . $db->error);
                    }
                    $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                    if (!$deleteStmt->execute()) {
                        error_log("Failed to execute delete statement for Abre_Courses: " . $deleteStmt->error);
                        $deleteStmt->close();
                        throw new Exception("Database execute error: " . $deleteStmt->error);
                    }
                    $deleteStmt->close();

                    $deleteRecords = false;
                }

                foreach ($classes["classes"] as $class) {
                    $classesProcessedCount++;
                    $classOrg = $class["school"]["sourcedId"];
                    $classOrg = trim($db->escape_string($classOrg));

                    $schoolCode = $schoolsCache[$classOrg]["id"];
                    $schoolCode = trim($db->escape_string($schoolCode));

                    $sectionCode = $class["sourcedId"];
                    $sectionCode = trim($db->escape_string($sectionCode));

                    if (isset($class["section_alias"])) {
                        $courseTitle = trim($db->escape_string($class["section_alias"]));
                    } else {
                        $courseTitle = trim($db->escape_string($class["title"]));
                    }

                    $courseCode = "";
                    if (isset($courseCache[$class["course"]["sourcedId"]])) {
                        $courseCode = trim($db->escape_string($courseCache[$class["course"]["sourcedId"]]["id"]));
                    } else {
                        // Course not in pre-built cache - use class sourcedId as fallback
                        error_log("WARNING: Course " . $class["course"]["sourcedId"] . " not found in pre-built cache, using class sourcedId as courseCode");
                        $courseCode = trim($db->escape_string($class["sourcedId"]));
                    }

                    if (!isset($classCache[$class["sourcedId"]])) {
                        $classCache[$class["sourcedId"]] = [
                            "terms" => $class["terms"],
                            "periods" => $class["periods"],
                            "sectionCode" => $sectionCode,
                            "courseCode" => $courseCode,
                            "title" => $courseTitle
                        ];
                    }

                    $teacher = [];
                    if (isset($teacherCache[$class["sourcedId"]])) {
                        $teacher = $teacherCache[$class["sourcedId"]];
                    }

                    foreach ($class["terms"] as $term) {
                        foreach ($class["periods"] as $period) {
                            $escapedPeriod = trim($db->escape_string($period));
                            $teacherID = isset($teacher["localId"]) ? trim($db->escape_string($teacher["localId"])) : "";

                            $cachedTerms = $gradingPeriods[$term["sourcedId"] . "-$siteID"] ?? null;
                            if ($cachedTerms && isset($cachedTerms["children"]) && is_array($cachedTerms["children"]) && count($cachedTerms["children"])) {
                                foreach ($cachedTerms["children"] as $childTerm) {
                                    $escapedTermCode = trim($db->escape_string($childTerm["sourcedId"] . "-$siteID"));
                                    $valuesToImport[] = "(
                    '$schoolCode', '$courseCode', '$sectionCode', '$teacherID',
                    '$escapedTermCode', '$escapedPeriod', $siteID, $schoolYearID
                  )";
                                }
                            } else {
                                $escapedTermCode = trim($db->escape_string($term["sourcedId"] . "-$siteID"));
                                $valuesToImport[] = "(
                  '$schoolCode', '$courseCode', '$sectionCode', '$teacherID',
                  '$escapedTermCode', '$escapedPeriod', $siteID, $schoolYearID
                )";
                            }

                            if (count($valuesToImport) >= MAX_IMPORT_LIMIT) {
                                insertRows($db, $dbColumns, $valuesToImport);
                                $valuesToImport = [];
                            }
                        }
                    }

                    // Stream classes to GCS
                    if ($classesStream !== false) {
                        _appendToGcsStream($classesStream, $class);
                    }
                }
            }
            $offset += $limit;
        } while (isset($classes["classes"]) && count($classes["classes"]));
        if (count($valuesToImport)) {
            insertRows($db, $dbColumns, $valuesToImport);
        }

        } finally {
            // Ensure stream is always finalized even if exceptions occur
            if ($classesStream !== false) {
                _finalizeGcsStream($classesStream);
            }
            $classes = null;
            unset($classesStream);
            gc_collect_cycles();
        }
        error_log("done with classes - processed: $classesProcessedCount classes");
        error_log("Memory usage after classes: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB");

        // Pre-build complete course cache first (for Synergy performance optimization)
        error_log("pre-building complete course cache...");
        $courseCacheOffset = 0;
        $courseCacheLimit = 1000;
        do {
            $allCourses = $apiGet("ims/oneroster/v1p1/courses", [
                "offset" => $courseCacheOffset,
                "limit" => $courseCacheLimit
            ]);
            if (isset($allCourses["courses"]) && count($allCourses["courses"])) {
                foreach ($allCourses["courses"] as $course) {
                    if (!isset($courseCache[$course["sourcedId"]])) {
                        $courseCode = $course["courseCode"] != "" ? $course["courseCode"] : $course["sourcedId"];
                        $courseCache[$course["sourcedId"]] = [
                            "id" => $courseCode,
                            "title" => $course["title"] ?? "Unknown Course"
                        ];
                    }
                }
                $courseCacheOffset += $courseCacheLimit;
                
                if ($courseCacheOffset % 5000 === 0) {
                    error_log("Pre-built course cache for $courseCacheOffset courses");
                }
            }
        } while (isset($allCourses["courses"]) && count($allCourses["courses"]));
        error_log("Completed pre-building course cache. Total courses cached: " . count($courseCache));

        // Pre-build complete class cache for all classes that might be referenced in enrollments
        error_log("pre-building complete class cache for enrollments...");
        $classCacheOffset = 0;
        $classCacheLimit = 1000;
        $classesFoundInCache = 0;
        do {
            $allClasses = $apiGet("ims/oneroster/v1p1/classes", [
                "offset" => $classCacheOffset,
                "limit" => $classCacheLimit
            ]);
            if ($allClasses !== false && isset($allClasses["classes"]) && is_array($allClasses["classes"]) && count($allClasses["classes"])) {
                error_log("DEBUG: Pre-caching " . count($allClasses["classes"]) . " classes from API (offset: $classCacheOffset)");
                foreach ($allClasses["classes"] as $class) {
                    if (!isset($class["sourcedId"]) || empty($class["sourcedId"])) {
                        error_log("WARNING: Class missing sourcedId during pre-cache build - skipping");
                        continue;
                    }
                    
                    if (!isset($classCache[$class["sourcedId"]])) {
                        $sectionCode = trim($db->escape_string($class["sourcedId"]));
                        $courseTitle = trim($db->escape_string($class["title"] ?? "Unknown Course"));
                        $courseCode = "";
                        
                        // Use pre-cached course data instead of individual API calls
                        if (isset($class["course"]["sourcedId"]) && isset($courseCache[$class["course"]["sourcedId"]])) {
                            $courseCode = trim($db->escape_string($courseCache[$class["course"]["sourcedId"]]["id"]));
                        }
                        
                        // Validate terms and periods arrays
                        $terms = isset($class["terms"]) && is_array($class["terms"]) ? $class["terms"] : [];
                        $periods = isset($class["periods"]) && is_array($class["periods"]) ? $class["periods"] : [];
                        
                        if (empty($terms)) {
                            error_log("WARNING: Class " . $class["sourcedId"] . " has empty or missing terms array");
                        }
                        if (empty($periods)) {
                            error_log("WARNING: Class " . $class["sourcedId"] . " has empty or missing periods array");
                        }
                        
                        $classCache[$class["sourcedId"]] = [
                            "terms" => $terms,
                            "periods" => $periods,
                            "sectionCode" => $sectionCode,
                            "courseCode" => $courseCode,
                            "title" => $courseTitle,
                            "courseSourcedId" => $class["course"]["sourcedId"] ?? ""
                        ];
                        $classesFoundInCache++;
                    }
                }
                $classCacheOffset += $classCacheLimit;
                
                if ($classCacheOffset % 10000 === 0) {
                    error_log("Pre-built class cache for $classCacheOffset classes, found $classesFoundInCache new classes");
                }
            } else {
                if ($allClasses === false) {
                    error_log("ERROR: Classes API call failed during pre-cache build (offset: $classCacheOffset)");
                } elseif (!isset($allClasses["classes"])) {
                    error_log("ERROR: Classes API response missing 'classes' field during pre-cache (offset: $classCacheOffset)");
                } elseif (!is_array($allClasses["classes"])) {
                    error_log("ERROR: Classes API response 'classes' field is not an array during pre-cache (offset: $classCacheOffset)");
                } else {
                    error_log("INFO: Classes API returned empty classes array during pre-cache (offset: $classCacheOffset)");
                }
                break; // Exit loop on API failure
            }
        } while (isset($allClasses["classes"]) && count($allClasses["classes"]));
        error_log("Completed pre-building class cache. Total classes cached: " . count($classCache) . " (found $classesFoundInCache new classes)");

        // Add diagnostic tracking for missing classes
        $missingClassIds = [];
        $enrollmentClassIds = [];

        error_log("starting enrollments");
        $limit = 1000;
        $offset = 0;
        
        // Wipe schedules for this site/year up-front to prevent duplicates on partial runs
        $deleteSql = "DELETE FROM Abre_StaffSchedules WHERE siteID = ? AND school_year_id = ?";
        $deleteStmt = $db->stmt_init();
        if (!$deleteStmt->prepare($deleteSql)) {
            error_log("Failed to prepare delete statement for Abre_StaffSchedules: " . $db->error);
            throw new Exception("Database prepare error: " . $db->error);
        }
        $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
        if (!$deleteStmt->execute()) {
            error_log("Failed to execute delete statement for Abre_StaffSchedules: " . $deleteStmt->error);
            $deleteStmt->close();
            throw new Exception("Database execute error: " . $deleteStmt->error);
        }
        $deleteStmt->close();

        $deleteSql = "DELETE FROM Abre_StudentSchedules WHERE siteID = ? AND school_year_id = ?";
        $deleteStmt = $db->stmt_init();
        if (!$deleteStmt->prepare($deleteSql)) {
            error_log("Failed to prepare delete statement for Abre_StudentSchedules: " . $db->error);
            throw new Exception("Database prepare error: " . $db->error);
        }
        $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
        if (!$deleteStmt->execute()) {
            error_log("Failed to execute delete statement for Abre_StudentSchedules: " . $deleteStmt->error);
            $deleteStmt->close();
            throw new Exception("Database execute error: " . $deleteStmt->error);
        }
        $deleteStmt->close();

        $deleteRecords = false;
        $valuesToImport = [
            "staff" => [],
            "student" => []
        ];
        
        // Add counters for debugging
        $staffEnrollmentCount = 0;
        $studentEnrollmentCount = 0;
        $staffDbColumns = "INSERT INTO Abre_StaffSchedules
                      (StaffID, SchoolCode, CourseCode, SectionCode, TermCode, Period,
                        CourseName, TeacherName, siteID, school_year_id)
                      VALUES";
        $studentDbColumns = "INSERT INTO Abre_StudentSchedules
                        (StudentID, FirstName, LastName, SchoolCode, CourseCode, SectionCode,
                          CourseName, StaffId, TeacherName, TermCode, Period, siteID, school_year_id)
                        VALUES";
        $enrollmentsStream = _initGcsStream('enrollments', $bucket, $currentDate, $siteID);
        
        if ($enrollmentsStream === false) {
            error_log("FATAL: Failed to initialize enrollments GCS stream - aborting enrollments processing");
            throw new Exception("Failed to initialize enrollments GCS stream");
        }

        try {
            do {
                $enrollments = $apiGet("ims/oneroster/v1p1/enrollments", [
                    "offset" => $offset,
                    "limit" => $limit
                ]);
            if (isset($enrollments["enrollments"]) && count($enrollments["enrollments"])) {
                foreach ($enrollments["enrollments"] as $enrollmentRecord) {
                    $enrollmentsProcessedCount++;
                    $userSourcedId = $enrollmentRecord["user"]["sourcedId"];
                    $classSourcedId = $enrollmentRecord["class"]["sourcedId"];
                    
                    // Debug logging for enrollment processing
                    if (!isset($enrollmentRecord["role"])) {
                        error_log("WARNING: Enrollment missing role for user $userSourcedId in class $classSourcedId");
                        continue;
                    }

                    $userID = "";
                    $firstName = "";
                    $lastName = "";
                    $fullName = "";

                    // BACKWARD COMPATIBLE FIX: Handle enrollment user ID mismatch by fetching user record dynamically
                    if (isset($usersCache[$userSourcedId])) {
                        // Standard case: enrollment user ID matches cache (existing sites continue working)
                        $userID = trim($db->escape_string($usersCache[$userSourcedId]["localId"]));
                        $firstName = trim($db->escape_string($usersCache[$userSourcedId]["firstName"]));
                        $lastName = trim($db->escape_string($usersCache[$userSourcedId]["lastName"]));
                        $fullName = trim($db->escape_string("$firstName $lastName"));
                    } else {
                        // Enrollment user ID doesn't match cache - fetch user record directly
                        // This handles sites where enrollment records use different internal GUIDs
                        error_log("DEBUG: User $userSourcedId not in cache, fetching directly from API");
                        $userRecord = $apiGet("ims/oneroster/v1p1/users/$userSourcedId");

                        if ($userRecord !== false && isset($userRecord["user"])) {
                            $user = $userRecord["user"];

                            // Apply the same studentId logic as used for student records
                            if (isset($config->oneRoster->studentId)) {
                                if ($config->oneRoster->studentId === 'sourcedId') {
                                    $userID = $user["sourcedId"];
                                } elseif ($config->oneRoster->studentId === 'identifier') {
                                    $userID = $user["identifier"];
                                } else {
                                    $userID = $user["identifier"] != "" ? $user["identifier"] : $user["sourcedId"];
                                }
                            } else {
                                $userID = $user["identifier"] != "" ? $user["identifier"] : $user["sourcedId"];
                            }

                            $userID = trim($db->escape_string($userID));
                            $firstName = trim($db->escape_string($user["givenName"] ?? ""));
                            $lastName = trim($db->escape_string($user["familyName"] ?? ""));
                            $fullName = trim($db->escape_string("$firstName $lastName"));

                            error_log("DEBUG: Fetched user directly: $userSourcedId -> $userID (Name: $fullName)");
                        } else {
                            error_log("WARNING: Could not fetch user record for $userSourcedId");
                            continue;
                        }
                    }

                    $courseSourcedId = "";
                    $terms = [];
                    $periods = [];
                    $sectionCode = "";
                    $courseName = "";
                    $courseCode = "";
                    if (isset($classCache[$classSourcedId])) {
                        $terms = $classCache[$classSourcedId]["terms"];
                        $periods = $classCache[$classSourcedId]["periods"];
                        $sectionCode = trim($db->escape_string($classCache[$classSourcedId]["sectionCode"]));
                        $courseName = trim($db->escape_string($classCache[$classSourcedId]["title"]));
                        $courseCode = $classCache[$classSourcedId]["courseCode"];
                        
                        // Debug logging for terms and periods
                        if (empty($terms) || empty($periods)) {
                            error_log("WARNING: Class $classSourcedId has empty terms or periods. Terms count: " . (is_array($terms) ? count($terms) : 'not array') . ", Periods count: " . (is_array($periods) ? count($periods) : 'not array'));
                        }
                        
                        // Skip processing if terms or periods are not valid arrays
                        if (!is_array($terms) || !is_array($periods) || empty($terms) || empty($periods)) {
                            error_log("SKIPPING: Class $classSourcedId - invalid or empty terms/periods data");
                            continue;
                        }
                    } else {
                        // Class not in cache - this should be rare now with comprehensive pre-caching
                        error_log("WARNING: Class $classSourcedId not found in pre-built cache, skipping enrollment");
                        error_log("DEBUG: Missing class details - Student: " . ($enrollmentRecord["user"]["sourcedId"] ?? 'unknown') . ", Role: " . ($enrollmentRecord["role"] ?? 'unknown'));
                        continue;
                    }

                    if (!isset($schoolsCache[$enrollmentRecord["school"]["sourcedId"]]["id"])) {
                        error_log("WARNING OR: School not found in cache for enrollment: " . $enrollmentRecord["school"]["sourcedId"]);
                        continue; // Skip this enrollment
                    }
                    $schoolCode = trim($db->escape_string($schoolsCache[$enrollmentRecord["school"]["sourcedId"]]["id"]));

                    if ($enrollmentRecord["role"] == "teacher" || $enrollmentRecord["role"] == "administrator") {
                        $staffEnrollmentCount++;
                        // Debug logging for staff schedules
                        if (empty($terms) || empty($periods)) {
                            error_log("Warning: Empty terms or periods for staff class $classSourcedId - terms count: " . (is_array($terms) ? count($terms) : 'not array') . ", periods count: " . (is_array($periods) ? count($periods) : 'not array'));
                        }
                        
                        // Skip processing if terms or periods are not valid arrays
                        if (!is_array($terms) || !is_array($periods) || empty($terms) || empty($periods)) {
                            error_log("SKIPPING staff schedule for Class $classSourcedId - invalid or empty terms/periods data");
                            continue;
                        }

                        foreach ($terms as $term) {
                            foreach ($periods as $period) {
                                $escapedPeriod = trim($db->escape_string($period));

                                $cachedTerms = $gradingPeriods[$term["sourcedId"] . "-$siteID"] ?? null;
                                if ($cachedTerms && isset($cachedTerms["children"]) && is_array($cachedTerms["children"]) && count($cachedTerms["children"])) {
                                    foreach ($cachedTerms["children"] as $childTerm) {
                                        $escapedTermCode = trim($db->escape_string($childTerm["sourcedId"] . "-$siteID"));
                                        $valuesToImport["staff"][] = "(
                      '$userID', '$schoolCode', '$courseCode', '$sectionCode', '$escapedTermCode',
                      '$escapedPeriod', '$courseName', '$fullName', $siteID, $schoolYearID
                    )";
                                    }
                                } else {
                                    $escapedTermCode = trim($db->escape_string($term["sourcedId"] . "-$siteID"));
                                    $valuesToImport["staff"][] = "(
                    '$userID', '$schoolCode', '$courseCode', '$sectionCode', '$escapedTermCode',
                    '$escapedPeriod', '$courseName', '$fullName', $siteID, $schoolYearID
                  )";
                                }

                                if (count($valuesToImport["staff"]) >= MAX_IMPORT_LIMIT) {
                                    insertRows($db, $staffDbColumns, $valuesToImport["staff"]);
                                    $valuesToImport["staff"] = [];
                                }
                            }
                        }
                    } elseif ($enrollmentRecord["role"] == "student") {
                        $studentEnrollmentCount++;
                        
                        // Skip processing if terms or periods are not valid arrays
                        if (!is_array($terms) || !is_array($periods) || empty($terms) || empty($periods)) {
                            error_log("SKIPPING student schedule for Class $classSourcedId - invalid or empty terms/periods data");
                            continue;
                        }

                        // Get primary teacher for this class (first teacher only to avoid duplicates)
                        $staffID = "";
                        $teacherName = "";
                        if (isset($classTeachersIndex[$classSourcedId]) && !empty($classTeachersIndex[$classSourcedId])) {
                            // Only use the first teacher to avoid creating duplicate student schedules
                            $primaryTeacher = $classTeachersIndex[$classSourcedId][0];
                            $staffID = $primaryTeacher['localId'];
                            $teacherName = trim($db->escape_string($primaryTeacher["firstName"] . " " . $primaryTeacher["lastName"]));
                        }

                        foreach ($terms as $term) {
                            foreach ($periods as $period) {
                                $escapedPeriod = trim($db->escape_string($period));
                                $cachedTerms = $gradingPeriods[$term["sourcedId"] . "-$siteID"] ?? null;
                                if ($cachedTerms && isset($cachedTerms["children"]) && is_array($cachedTerms["children"]) && count($cachedTerms["children"])) {
                                    foreach ($cachedTerms["children"] as $childTerm) {
                                        $escapedTerm = trim($db->escape_string($childTerm["sourcedId"] . "-$siteID"));
                                        $valuesToImport["student"][] = "(
                              '$userID', '$firstName', '$lastName', '$schoolCode', '$courseCode', '$sectionCode',
                              '$courseName', '$staffID', '$teacherName', '$escapedTerm', '$escapedPeriod', $siteID, $schoolYearID
                            )";
                                    }
                                } else {
                                    $escapedTerm = trim($db->escape_string($term["sourcedId"] . "-$siteID"));
                                    $valuesToImport["student"][] = "(
                            '$userID', '$firstName', '$lastName', '$schoolCode', '$courseCode', '$sectionCode',
                            '$courseName', '$staffID', '$teacherName', '$escapedTerm', '$escapedPeriod', $siteID, $schoolYearID
                          )";
                                }

                                if (count($valuesToImport["student"]) >= MAX_IMPORT_LIMIT) {
                                    insertRows($db, $studentDbColumns, $valuesToImport["student"]);
                                    $valuesToImport["student"] = [];
                                    
                                    // Check memory usage after batch processing
                                    $currentMemory = memory_get_usage(true);
                                    if ($currentMemory > $memoryWarningThreshold) {
                                        error_log("Memory usage high during enrollment batch processing: " . round($currentMemory / 1024 / 1024, 2) . " MB");
                                        // Force garbage collection
                                        gc_collect_cycles();
                                    }
                                }
                            }
                        }
                    }

                    // Stream enrollments to GCS
                    if ($enrollmentsStream !== false) {
                        _appendToGcsStream($enrollmentsStream, $enrollmentRecord);
                    }
                }
            }
            $offset += $limit;

                // Periodic memory cleanup for large sites
                if ($offset % 10000 === 0) {
                    gc_collect_cycles();
                    error_log("Processed $offset enrollments, memory usage: " . memory_get_usage(true) / 1024 / 1024 . " MB");
                }
            } while (isset($enrollments["enrollments"]) && count($enrollments["enrollments"]));
            
            // Log enrollment processing summary
            error_log("Enrollment processing summary:");
            error_log("  Total enrollments processed: $enrollmentsProcessedCount");
            error_log("  Staff enrollments: $staffEnrollmentCount");
            error_log("  Student enrollments: $studentEnrollmentCount");
            
            if (count($valuesToImport["staff"])) {
                insertRows($db, $staffDbColumns, $valuesToImport["staff"]);
            }
            if (count($valuesToImport["student"])) {
                insertRows($db, $studentDbColumns, $valuesToImport["student"]);
            }

            // Log final counts for debugging
            $totalStaffSchedules = 0;
            $totalStudentSchedules = 0;
            
            // Count total staff schedules processed
            $staffCountQuery = "SELECT COUNT(*) as count FROM Abre_StaffSchedules WHERE siteID = ? AND school_year_id = ?";
            $staffStmt = $db->stmt_init();
            if (!$staffStmt->prepare($staffCountQuery)) {
                error_log("Failed to prepare select statement for Abre_StaffSchedules count: " . $db->error);
                throw new Exception("Database prepare error: " . $db->error);
            }
            $staffStmt->bind_param("ii", $siteID, $schoolYearID);
            if (!$staffStmt->execute()) {
                error_log("Failed to execute select statement for Abre_StaffSchedules count: " . $staffStmt->error);
                $staffStmt->close();
                throw new Exception("Database execute error: " . $staffStmt->error);
            }
            $staffResult = $staffStmt->get_result();
            if ($staffRow = $staffResult->fetch_assoc()) {
                $totalStaffSchedules = $staffRow['count'];
            }
            $staffStmt->close();
            
            // Count total student schedules processed
            $studentCountQuery = "SELECT COUNT(*) as count FROM Abre_StudentSchedules WHERE siteID = ? AND school_year_id = ?";
            $studentStmt = $db->stmt_init();
            if (!$studentStmt->prepare($studentCountQuery)) {
                error_log("Failed to prepare select statement for Abre_StudentSchedules count: " . $db->error);
                throw new Exception("Database prepare error: " . $db->error);
            }
            $studentStmt->bind_param("ii", $siteID, $schoolYearID);
            if (!$studentStmt->execute()) {
                error_log("Failed to execute select statement for Abre_StudentSchedules count: " . $studentStmt->error);
                $studentStmt->close();
                throw new Exception("Database execute error: " . $studentStmt->error);
            }
            $studentResult = $studentStmt->get_result();
            if ($studentRow = $studentResult->fetch_assoc()) {
                $totalStudentSchedules = $studentRow['count'];
            }
            $studentStmt->close();
            
            error_log("Final counts - Staff Schedules: $totalStaffSchedules, Student Schedules: $totalStudentSchedules");

        } finally {
            // Ensure stream is always finalized even if exceptions occur
            if ($enrollmentsStream !== false) {
                _finalizeGcsStream($enrollmentsStream);
            }
            $enrollments = null;
            unset($enrollmentsStream);
        }

        // Final cleanup of large caches
        unset($usersCache, $classTeachersIndex, $classCache, $courseCache, $teacherCache);
        gc_collect_cycles();
        error_log("done with enrollments - processed: $enrollmentsProcessedCount enrollments");
        error_log("Memory usage after enrollments: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB");
        
        // Final comprehensive reporting
        $jobEndTime = (new DateTime("now", new DateTimeZone('UTC')))->format("Y-m-d H:i:s");
        error_log("=== FINAL IMPORT SUMMARY ===");
        error_log("Job Start Time: $jobStartTime");
        error_log("Job End Time: $jobEndTime");
        error_log("----------------------------");
        error_log("Schools processed: $schoolsProcessedCount");
        error_log("Academic Sessions processed: $academicSessionsProcessedCount");
        error_log("Students processed: $studentsProcessedCount");
        error_log("Staff processed: $staffProcessedCount");
        error_log("Courses processed: $coursesProcessedCount");
        error_log("Classes processed: $classesProcessedCount");
        error_log("Enrollments processed: $enrollmentsProcessedCount");
        error_log("----------------------------");
        $totalRecords = $schoolsProcessedCount + $academicSessionsProcessedCount + $studentsProcessedCount + $staffProcessedCount + $coursesProcessedCount + $classesProcessedCount + $enrollmentsProcessedCount;
        error_log("Total records processed: $totalRecords");
        error_log("Final Memory Usage: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB");
        error_log("Peak Memory Usage: " . round(memory_get_peak_usage(true) / 1024 / 1024, 2) . " MB");
        error_log("=== END SUMMARY ===");
    } catch (Exception $ex) {
        $error = $ex->getMessage();
        error_log("FATAL ERROR: " . $error);
        error_log("DEBUG: Exception occurred at line " . $ex->getLine() . " in file " . $ex->getFile());
        error_log("DEBUG: Stack trace: " . $ex->getTraceAsString());
        
        // Log partial progress in case of failure
        error_log("=== PARTIAL PROGRESS BEFORE ERROR ===");
        error_log("Schools processed: " . (isset($schoolsProcessedCount) ? $schoolsProcessedCount : 0));
        error_log("Academic Sessions processed: " . (isset($academicSessionsProcessedCount) ? $academicSessionsProcessedCount : 0));
        error_log("Students processed: " . (isset($studentsProcessedCount) ? $studentsProcessedCount : 0));
        error_log("Staff processed: " . (isset($staffProcessedCount) ? $staffProcessedCount : 0));
        error_log("Courses processed: " . (isset($coursesProcessedCount) ? $coursesProcessedCount : 0));
        error_log("Classes processed: " . (isset($classesProcessedCount) ? $classesProcessedCount : 0));
        error_log("Enrollments processed: " . (isset($enrollmentsProcessedCount) ? $enrollmentsProcessedCount : 0));
        error_log("Memory usage at error: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB");
        error_log("=====================================");
    }

    $details = [];
    if (isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    } else {
        $status = CRON_SUCCESS;
    }

    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
